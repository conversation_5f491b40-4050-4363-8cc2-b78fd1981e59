#!/usr/bin/env python3
"""
Test OMR processor với ảnh thật
"""

import cv2
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.services.omr_debug_processor import OMRDebugProcessor


def test_real_image():
    """Test với ảnh thật"""

    # Tìm ảnh thật trong thư mục data/grading
    grading_dir = Path("data/grading")
    image_files = []

    for ext in ["*.jpg", "*.jpeg", "*.png"]:
        image_files.extend(grading_dir.glob(ext))

    if not image_files:
        print("❌ Không tìm thấy ảnh nào trong data/grading")
        return

    # Chọn ảnh đầu tiên
    image_path = image_files[0]
    print(f"[TEST] Testing with real image: {image_path}")

    # Đọc ảnh
    image = cv2.imread(str(image_path))
    if image is None:
        print(f"❌ Không thể đọc ảnh: {image_path}")
        return

    print(f"[SIZE] Image size: {image.shape}")

    # Khởi tạo processor
    processor = OMRDebugProcessor()

    # Xử lý ảnh
    result = processor.process_image(image)

    # In kết quả
    print(f"\n[RESULT] Student ID: {result.get('student_id', 'N/A')}")
    print(f"[RESULT] Test Code: {result.get('test_code', 'N/A')}")

    answers = result.get("answers", [])
    print(
        f"[RESULT] Total answers: {len(answers) if isinstance(answers, (list, dict)) else 'N/A'}"
    )
    print(f"[RESULT] Answers type: {type(answers)}")

    if isinstance(answers, list) and answers:
        print(f"[RESULT] First 10 answers: {answers[:10]}")
    elif isinstance(answers, dict):
        print(f"[RESULT] Answers dict keys: {list(answers.keys())[:10]}")
    else:
        print(f"[RESULT] No answers detected or unexpected format")

    # Kiểm tra debug files
    debug_dir = Path("data/grading/debug")
    if debug_dir.exists():
        debug_files = list(debug_dir.glob("*.jpg"))
        print(f"\n[DEBUG] Created {len(debug_files)} debug files:")
        for file in debug_files:
            print(f"  - {file.name}")

    print(f"\n[DONE] Processing completed!")


if __name__ == "__main__":
    test_real_image()
