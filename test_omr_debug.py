#!/usr/bin/env python3
"""
Script test OMR Debug Processor
Chạy xử lý phiếu tr<PERSON>c nghiệm với debug chi tiết
"""

import sys
import os
import asyncio
from pathlib import Path

# Thêm thư mục gốc vào Python path
sys.path.append(str(Path(__file__).parent))

from app.services.omr_debug_processor import OMRDebugProcessor
import logging

# Cấu hình logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)


def main():
    """Hàm chính test OMR processor"""

    # Đường dẫn ảnh test
    image_path = "data/grading/test_images/1.jpeg"

    # Kiểm tra file tồn tại
    if not os.path.exists(image_path):
        print(f"Image file not found: {image_path}")
        return

    print(f"Processing image: {image_path}")

    # Tạo processor
    processor = OMRDebugProcessor()

    # Xử lý ảnh
    result = processor.process_answer_sheet(image_path)

    # In kết quả
    print("\n" + "=" * 50)
    print("PROCESSING RESULTS")
    print("=" * 50)

    if result["success"]:
        print(f"Success!")
        print(f"Student ID: {result['student_id']}")
        print(f"Test Code: {result['test_code']}")
        print(f"Total answers: {len(result['answers'])}")

        # In một số câu trả lời mẫu
        print("\nAnswers (first 10):")
        answers = result["answers"]
        for i, (q_num, answer) in enumerate(list(answers.items())[:10]):
            print(f"   Q{q_num}: {answer}")

        if len(answers) > 10:
            print(f"   ... and {len(answers) - 10} more")

        print(f"\nDebug images saved at: {result['debug_dir']}")

        # Liệt kê các file debug
        debug_dir = Path(result["debug_dir"])
        if debug_dir.exists():
            debug_files = list(debug_dir.glob("*.jpg"))
            debug_files.sort()

            print(f"\nDebug images list ({len(debug_files)} files):")
            for file in debug_files:
                print(f"   - {file.name}")

    else:
        print(f"Processing failed: {result['error']}")

    print("\n" + "=" * 50)


if __name__ == "__main__":
    main()
